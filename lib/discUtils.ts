/**
 * Disc Data Utilities for the Disc Golf Inventory Management System
 *
 * This module provides utility functions for disc data manipulation including
 * unique ID generation, display name formatting, collection statistics,
 * and data export/import helpers.
 */

import type { Disc, FlightNumbers } from "./types";
import { DiscCondition, Location } from "./types";

// ============================================================================
// ID GENERATION UTILITIES
// ============================================================================

/**
 * Generate a unique disc ID using crypto.randomUUID()
 * Falls back to a custom implementation if crypto.randomUUID is not available
 *
 * @returns A unique UUID v4 string
 */
export function generateDiscId(): string {
  if (typeof crypto !== "undefined" && crypto.randomUUID) {
    return crypto.randomUUID();
  }

  // Fallback implementation for environments without crypto.randomUUID
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Validate if a string is a valid UUID format
 *
 * @param id - The ID string to validate
 * @returns True if the ID is a valid UUID format
 */
export function isValidDiscId(id: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

// ============================================================================
// DISPLAY NAME FORMATTING UTILITIES
// ============================================================================

/**
 * Format a disc's display name in a consistent format
 * Format: "Manufacturer Mold (Plastic)"
 *
 * @param disc - The disc object
 * @returns Formatted display name
 */
export function formatDiscDisplayName(disc: Pick<Disc, "manufacturer" | "mold" | "plasticType">): string {
  return `${disc.manufacturer} ${disc.mold} (${disc.plasticType})`;
}

/**
 * Format a disc's full description including weight and condition
 * Format: "Manufacturer Mold (Plastic) - 175g - Lightly Used"
 *
 * @param disc - The disc object
 * @returns Formatted full description
 */
export function formatDiscFullDescription(
  disc: Pick<Disc, "manufacturer" | "mold" | "plasticType" | "weight" | "condition">
): string {
  const baseName = formatDiscDisplayName(disc);
  const conditionText = formatConditionText(disc.condition);
  return `${baseName} - ${disc.weight}g - ${conditionText}`;
}

/**
 * Format flight numbers as a readable string
 * Format: "Speed: 12 | Glide: 5 | Turn: -1 | Fade: 3"
 *
 * @param flightNumbers - The flight numbers object
 * @returns Formatted flight numbers string
 */
export function formatFlightNumbers(flightNumbers: FlightNumbers): string {
  return `Speed: ${flightNumbers.speed} | Glide: ${flightNumbers.glide} | Turn: ${flightNumbers.turn} | Fade: ${flightNumbers.fade}`;
}

/**
 * Format flight numbers as a compact string
 * Format: "12|5|-1|3"
 *
 * @param flightNumbers - The flight numbers object
 * @returns Compact flight numbers string
 */
export function formatFlightNumbersCompact(flightNumbers: FlightNumbers): string {
  return `${flightNumbers.speed}|${flightNumbers.glide}|${flightNumbers.turn}|${flightNumbers.fade}`;
}

/**
 * Convert condition enum to human-readable text
 *
 * @param condition - The disc condition enum value
 * @returns Human-readable condition text
 */
export function formatConditionText(condition: DiscCondition): string {
  const conditionMap: Record<DiscCondition, string> = {
    [DiscCondition.NEW]: "New",
    [DiscCondition.LIGHTLY_USED]: "Lightly Used",
    [DiscCondition.MODERATELY_USED]: "Moderately Used",
    [DiscCondition.WELL_USED]: "Well Used",
    [DiscCondition.BEAT_IN]: "Beat In",
  };
  return conditionMap[condition];
}

/**
 * Convert location enum to human-readable text
 *
 * @param location - The disc location enum value
 * @returns Human-readable location text
 */
export function formatLocationText(location: Location): string {
  const locationMap: Record<Location, string> = {
    [Location.BAG]: "In Bag",
    [Location.CAR]: "In Car",
    [Location.HOME]: "At Home",
    [Location.LOANED]: "Loaned Out",
    [Location.LOST]: "Lost",
  };
  return locationMap[location];
}

// ============================================================================
// COLLECTION STATISTICS UTILITIES
// ============================================================================

/**
 * Calculate the total value of a disc collection
 *
 * @param discs - Array of discs
 * @returns Total purchase price of all discs with purchase prices
 */
export function calculateCollectionValue(discs: Disc[]): number {
  return discs.reduce((total, disc) => {
    return total + (disc.purchasePrice || 0);
  }, 0);
}

/**
 * Calculate the average weight of a disc collection
 *
 * @param discs - Array of discs
 * @returns Average weight in grams, or 0 if no discs
 */
export function calculateAverageWeight(discs: Disc[]): number {
  if (discs.length === 0) return 0;

  const totalWeight = discs.reduce((total, disc) => total + disc.weight, 0);
  return Math.round((totalWeight / discs.length) * 100) / 100; // Round to 2 decimal places
}

/**
 * Get the most common manufacturer in a collection
 *
 * @param discs - Array of discs
 * @returns The most common manufacturer name, or null if no discs
 */
export function getMostCommonManufacturer(discs: Disc[]): string | null {
  if (discs.length === 0) return null;

  const manufacturerCounts = discs.reduce((counts, disc) => {
    counts[disc.manufacturer] = (counts[disc.manufacturer] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);

  return Object.entries(manufacturerCounts).reduce((mostCommon, [manufacturer, count]) => {
    return count > (manufacturerCounts[mostCommon] || 0) ? manufacturer : mostCommon;
  }, Object.keys(manufacturerCounts)[0]);
}

/**
 * Get the most common mold in a collection
 *
 * @param discs - Array of discs
 * @returns The most common mold name, or null if no discs
 */
export function getMostCommonMold(discs: Disc[]): string | null {
  if (discs.length === 0) return null;

  const moldCounts = discs.reduce((counts, disc) => {
    counts[disc.mold] = (counts[disc.mold] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);

  return Object.entries(moldCounts).reduce((mostCommon, [mold, count]) => {
    return count > (moldCounts[mostCommon] || 0) ? mold : mostCommon;
  }, Object.keys(moldCounts)[0]);
}

/**
 * Count discs by condition
 *
 * @param discs - Array of discs
 * @returns Object with condition counts
 */
export function countDiscsByCondition(discs: Disc[]): Record<DiscCondition, number> {
  const counts = {} as Record<DiscCondition, number>;

  // Initialize all conditions to 0
  Object.values(DiscCondition).forEach((condition) => {
    counts[condition] = 0;
  });

  // Count discs by condition
  discs.forEach((disc) => {
    counts[disc.condition]++;
  });

  return counts;
}

/**
 * Count discs by location
 *
 * @param discs - Array of discs
 * @returns Object with location counts
 */
export function countDiscsByLocation(discs: Disc[]): Record<Location, number> {
  const counts = {} as Record<Location, number>;

  // Initialize all locations to 0
  Object.values(Location).forEach((location) => {
    counts[location] = 0;
  });

  // Count discs by location
  discs.forEach((disc) => {
    counts[disc.currentLocation]++;
  });

  return counts;
}

/**
 * Count discs by manufacturer
 *
 * @param discs - Array of discs
 * @returns Object with manufacturer counts
 */
export function countDiscsByManufacturer(discs: Disc[]): Record<string, number> {
  return discs.reduce((counts, disc) => {
    counts[disc.manufacturer] = (counts[disc.manufacturer] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);
}

/**
 * Calculate average flight numbers across a collection
 *
 * @param discs - Array of discs
 * @returns Average flight numbers, or null if no discs
 */
export function calculateAverageFlightNumbers(discs: Disc[]): FlightNumbers | null {
  if (discs.length === 0) return null;

  const totals = discs.reduce(
    (acc, disc) => ({
      speed: acc.speed + disc.flightNumbers.speed,
      glide: acc.glide + disc.flightNumbers.glide,
      turn: acc.turn + disc.flightNumbers.turn,
      fade: acc.fade + disc.flightNumbers.fade,
    }),
    { speed: 0, glide: 0, turn: 0, fade: 0 }
  );

  return {
    speed: Math.round((totals.speed / discs.length) * 10) / 10, // Round to 1 decimal
    glide: Math.round((totals.glide / discs.length) * 10) / 10,
    turn: Math.round((totals.turn / discs.length) * 10) / 10,
    fade: Math.round((totals.fade / discs.length) * 10) / 10,
  };
}

// ============================================================================
// DATA EXPORT/IMPORT HELPERS
// ============================================================================

/**
 * Export disc collection to JSON string with pretty formatting
 *
 * @param discs - Array of discs to export
 * @param indent - Number of spaces for indentation (default: 2)
 * @returns JSON string representation of the collection
 */
export function exportDiscsToJson(discs: Disc[], indent = 2): string {
  try {
    return JSON.stringify(discs, null, indent);
  } catch (error) {
    console.error("Failed to export discs to JSON:", error);
    return "[]";
  }
}

/**
 * Export disc collection to CSV format
 *
 * @param discs - Array of discs to export
 * @returns CSV string representation of the collection
 */
export function exportDiscsToCSV(discs: Disc[]): string {
  if (discs.length === 0) {
    return "No discs to export";
  }

  // CSV headers
  const headers = [
    "ID",
    "Manufacturer",
    "Mold",
    "Plastic Type",
    "Weight",
    "Condition",
    "Speed",
    "Glide",
    "Turn",
    "Fade",
    "Color",
    "Notes",
    "Purchase Date",
    "Purchase Price",
    "Current Location",
    "Image URL",
    "Created At",
    "Updated At",
  ];

  // Convert discs to CSV rows
  const rows = discs.map((disc) => [
    disc.id,
    disc.manufacturer,
    disc.mold,
    disc.plasticType,
    disc.weight.toString(),
    formatConditionText(disc.condition),
    disc.flightNumbers.speed.toString(),
    disc.flightNumbers.glide.toString(),
    disc.flightNumbers.turn.toString(),
    disc.flightNumbers.fade.toString(),
    disc.color,
    disc.notes || "",
    disc.purchaseDate?.toISOString().split("T")[0] || "",
    disc.purchasePrice?.toString() || "",
    formatLocationText(disc.currentLocation),
    disc.imageUrl || "",
    disc.createdAt.toISOString(),
    disc.updatedAt.toISOString(),
  ]);

  // Combine headers and rows
  const csvContent = [headers, ...rows]
    .map((row) => row.map((field) => `"${field.replace(/"/g, '""')}"`).join(","))
    .join("\n");

  return csvContent;
}

/**
 * Parse JSON string and validate disc data
 *
 * @param jsonData - JSON string containing disc data
 * @returns Object with success status and parsed discs or error message
 */
export function parseDiscsFromJson(jsonData: string): { success: boolean; discs?: Disc[]; error?: string } {
  try {
    const parsed = JSON.parse(jsonData);

    if (!Array.isArray(parsed)) {
      return { success: false, error: "Data must be an array of discs" };
    }

    // Basic validation - check if objects have required disc properties
    const requiredFields = [
      "id",
      "manufacturer",
      "mold",
      "plasticType",
      "weight",
      "condition",
      "flightNumbers",
      "color",
      "currentLocation",
    ];

    for (const item of parsed) {
      if (typeof item !== "object" || item === null) {
        return { success: false, error: "Invalid disc object found" };
      }

      for (const field of requiredFields) {
        if (!(field in item)) {
          return { success: false, error: `Missing required field: ${field}` };
        }
      }
    }

    return { success: true, discs: parsed as Disc[] };
  } catch {
    return { success: false, error: "Invalid JSON format" };
  }
}

/**
 * Create a summary of collection changes for import operations
 *
 * @param currentDiscs - Current disc collection
 * @param newDiscs - New disc collection to import
 * @returns Summary of changes that would be made
 */
export function createImportSummary(
  currentDiscs: Disc[],
  newDiscs: Disc[]
): {
  totalCurrent: number;
  totalNew: number;
  newDiscIds: string[];
  duplicateIds: string[];
  wouldReplace: boolean;
} {
  const currentIds = new Set(currentDiscs.map((disc) => disc.id));

  const newDiscIds = newDiscs.filter((disc) => !currentIds.has(disc.id)).map((disc) => disc.id);
  const duplicateIds = newDiscs.filter((disc) => currentIds.has(disc.id)).map((disc) => disc.id);

  return {
    totalCurrent: currentDiscs.length,
    totalNew: newDiscs.length,
    newDiscIds,
    duplicateIds,
    wouldReplace: newDiscs.length > 0 && currentDiscs.length > 0,
  };
}

// ============================================================================
// SEARCH AND FILTER UTILITIES
// ============================================================================

/**
 * Search discs by text query across multiple fields
 *
 * @param discs - Array of discs to search
 * @param query - Search query string
 * @returns Filtered array of discs matching the query
 */
export function searchDiscs(discs: Disc[], query: string): Disc[] {
  if (!query.trim()) {
    return discs;
  }

  const searchTerm = query.toLowerCase().trim();

  return discs.filter((disc) => {
    const searchableText = [
      disc.manufacturer,
      disc.mold,
      disc.plasticType,
      disc.color,
      disc.notes || "",
      formatConditionText(disc.condition),
      formatLocationText(disc.currentLocation),
      disc.weight.toString(),
      formatFlightNumbersCompact(disc.flightNumbers),
    ]
      .join(" ")
      .toLowerCase();

    return searchableText.includes(searchTerm);
  });
}

/**
 * Filter discs by multiple criteria
 *
 * @param discs - Array of discs to filter
 * @param filters - Filter criteria object
 * @returns Filtered array of discs
 */
export function filterDiscs(
  discs: Disc[],
  filters: {
    manufacturers?: string[];
    conditions?: DiscCondition[];
    locations?: Location[];
    weightRange?: { min: number; max: number };
    speedRange?: { min: number; max: number };
  }
): Disc[] {
  return discs.filter((disc) => {
    // Filter by manufacturers
    if (filters.manufacturers && filters.manufacturers.length > 0) {
      if (!filters.manufacturers.includes(disc.manufacturer)) {
        return false;
      }
    }

    // Filter by conditions
    if (filters.conditions && filters.conditions.length > 0) {
      if (!filters.conditions.includes(disc.condition)) {
        return false;
      }
    }

    // Filter by locations
    if (filters.locations && filters.locations.length > 0) {
      if (!filters.locations.includes(disc.currentLocation)) {
        return false;
      }
    }

    // Filter by weight range
    if (filters.weightRange) {
      if (disc.weight < filters.weightRange.min || disc.weight > filters.weightRange.max) {
        return false;
      }
    }

    // Filter by speed range
    if (filters.speedRange) {
      if (disc.flightNumbers.speed < filters.speedRange.min || disc.flightNumbers.speed > filters.speedRange.max) {
        return false;
      }
    }

    return true;
  });
}

/**
 * Sort discs by specified criteria
 *
 * @param discs - Array of discs to sort
 * @param sortBy - Sort criteria
 * @param sortOrder - Sort order (asc or desc)
 * @returns Sorted array of discs
 */
export function sortDiscs(
  discs: Disc[],
  sortBy: "name" | "manufacturer" | "weight" | "speed" | "condition" | "location" | "createdAt" | "updatedAt",
  sortOrder: "asc" | "desc" = "asc"
): Disc[] {
  const sorted = [...discs].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case "name":
        comparison = formatDiscDisplayName(a).localeCompare(formatDiscDisplayName(b));
        break;
      case "manufacturer":
        comparison = a.manufacturer.localeCompare(b.manufacturer);
        break;
      case "weight":
        comparison = a.weight - b.weight;
        break;
      case "speed":
        comparison = a.flightNumbers.speed - b.flightNumbers.speed;
        break;
      case "condition":
        comparison = a.condition.localeCompare(b.condition);
        break;
      case "location":
        comparison = a.currentLocation.localeCompare(b.currentLocation);
        break;
      case "createdAt":
        comparison = a.createdAt.getTime() - b.createdAt.getTime();
        break;
      case "updatedAt":
        comparison = a.updatedAt.getTime() - b.updatedAt.getTime();
        break;
      default:
        comparison = 0;
    }

    return sortOrder === "desc" ? -comparison : comparison;
  });

  return sorted;
}
